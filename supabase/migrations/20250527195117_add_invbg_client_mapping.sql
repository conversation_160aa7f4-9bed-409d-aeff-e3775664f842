-- Create table to map our client IDs to inv.bg client IDs
CREATE TABLE invbg_client_mapping (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    invbg_client_id INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure one-to-one mapping
    UNIQUE(client_id),
    UNIQUE(invbg_client_id)
);

-- Add comments for clarity
COMMENT ON TABLE invbg_client_mapping IS 'Maps our client IDs to inv.bg client IDs';
COMMENT ON COLUMN invbg_client_mapping.client_id IS 'Our internal client ID';
COMMENT ON COLUMN invbg_client_mapping.invbg_client_id IS 'inv.bg client ID';

-- Add index for faster lookups
CREATE INDEX idx_invbg_client_mapping_client_id ON invbg_client_mapping(client_id);
CREATE INDEX idx_invbg_client_mapping_invbg_client_id ON invbg_client_mapping(invbg_client_id);
