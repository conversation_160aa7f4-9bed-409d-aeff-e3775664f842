{"name": "proposals", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "serve:ssr:proposals": "node dist/proposals/server/server.mjs"}, "private": true, "dependencies": {"@angular/animations": "^19.2.0", "@angular/common": "^19.2.0", "@angular/compiler": "^19.2.0", "@angular/core": "^19.2.0", "@angular/forms": "^19.2.0", "@angular/platform-browser": "^19.2.0", "@angular/platform-browser-dynamic": "^19.2.0", "@angular/platform-server": "^19.2.0", "@angular/router": "^19.2.0", "@angular/ssr": "^19.2.12", "@primeng/themes": "^19.1.3", "@supabase/supabase-js": "^2.49.8", "@tailwindcss/postcss": "^4.1.7", "aos": "^2.3.4", "axios": "^1.9.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "openai": "^4.103.0", "postcss": "^8.5.3", "primeicons": "^7.0.0", "primeng": "^19.1.3", "rxjs": "~7.8.0", "stripe": "^18.1.1", "tailwindcss": "^4.1.7", "tslib": "^2.3.0", "zod": "^3.25.28", "zod-to-json-schema": "^3.24.5", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.12", "@angular/cli": "^19.2.12", "@angular/compiler-cli": "^19.2.0", "@types/aos": "^3.0.7", "@types/cookie-parser": "^1.4.8", "@types/express": "^4.17.17", "@types/jasmine": "~5.1.0", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^18.18.0", "jasmine-core": "~5.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "prettier": "3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "typescript": "~5.7.2"}}