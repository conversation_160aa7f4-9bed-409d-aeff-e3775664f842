import { z } from 'zod';

export const BillingInformationSchema = z.object({
  id: z.string().uuid(),
  client_id: z.string().uuid(),
  company_name: z.string().nullable(),
  vat_number: z.string().nullable(),
  eik: z.string().nullable(),
  mol: z.string().nullable(),
  address: z.string().nullable(),
  country: z.string().nullable(),
  city: z.string().nullable(),
  is_bulgarian: z.boolean(),
  created_at: z.string(),
});

export const BillingInformationRequestSchema = z.object({
  company_name: z.string().min(1, 'Company name is required'),
  vat_number: z.string().optional(),
  eik: z.string().optional(),
  mol: z.string().optional(),
  address: z.string().min(1, 'Address is required'),
  country: z.string().min(1, 'Country is required'),
  city: z.string().min(1, 'City is required'),
  is_bulgarian: z.boolean().default(false),
}).refine((data) => {
  // If Bulgarian company, EIK and MOL are required
  if (data.is_bulgarian || data.country === 'Bulgaria') {
    return data.eik && data.eik.length > 0 && data.mol && data.mol.length > 0;
  }
  return true;
}, {
  message: 'EIK and MOL are required for Bulgarian companies',
  path: ['eik'], // This will show the error on the eik field
});

export const ClientRequestSchema = z.object({
  name: z.string(),
  rep_full_name: z.string(),
  email: z.string().email(),
});

export type BillingInformation = z.infer<typeof BillingInformationSchema>;
export type BillingInformationRequest = z.infer<typeof BillingInformationRequestSchema>;
export type ClientRequest = z.infer<typeof ClientRequestSchema>;
