import { z } from 'zod';

export const BillingInformationSchema = z.object({
  id: z.string().uuid(),
  client_id: z.string().uuid(),
  company_name: z.string().nullable(),
  vat_number: z.string().nullable(),
  eik: z.string().nullable(),
  mol: z.string().nullable(),
  address: z.string().nullable(),
  country: z.string().nullable(),
  city: z.string().nullable(),
  is_bulgarian: z.boolean(),
  created_at: z.string(),
});

export const ClientRequestSchema = z.object({
  name: z.string(),
  rep_full_name: z.string(),
  email: z.string().email(),
});

export type BillingInformation = z.infer<typeof BillingInformationSchema>;
export type ClientRequest = z.infer<typeof ClientRequestSchema>;
