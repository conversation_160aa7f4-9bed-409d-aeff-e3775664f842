export interface CountryMapping {
  en: string;
  bg: string;
}

export const COUNTRY_MAPPINGS: Record<string, CountryMapping> = {
  'Afghanistan': { en: 'Afghanistan', bg: 'Афганистан' },
  'Albania': { en: 'Albania', bg: 'Албания' },
  'Algeria': { en: 'Algeria', bg: 'Алжир' },
  'Andorra': { en: 'Andorra', bg: 'Андора' },
  'Angola': { en: 'Angola', bg: 'Ангола' },
  'Antigua and Barbuda': { en: 'Antigua and Barbuda', bg: 'Антигуа и Барбуда' },
  'Argentina': { en: 'Argentina', bg: 'Аржентина' },
  'Armenia': { en: 'Armenia', bg: 'Армения' },
  'Australia': { en: 'Australia', bg: 'Австралия' },
  'Austria': { en: 'Austria', bg: 'Австрия' },
  'Azerbaijan': { en: 'Azerbaijan', bg: 'Азербайджан' },
  'Bahamas': { en: 'Bahamas', bg: 'Бахами' },
  'Bahrain': { en: 'Bahrain', bg: 'Бахрейн' },
  'Bangladesh': { en: 'Bangladesh', bg: 'Бангладеш' },
  'Barbados': { en: 'Barbados', bg: 'Барбадос' },
  'Belarus': { en: 'Belarus', bg: 'Беларус' },
  'Belgium': { en: 'Belgium', bg: 'Белгия' },
  'Belize': { en: 'Belize', bg: 'Белиз' },
  'Benin': { en: 'Benin', bg: 'Бенин' },
  'Bhutan': { en: 'Bhutan', bg: 'Бутан' },
  'Bolivia': { en: 'Bolivia', bg: 'Боливия' },
  'Bosnia and Herzegovina': { en: 'Bosnia and Herzegovina', bg: 'Босна и Херцеговина' },
  'Botswana': { en: 'Botswana', bg: 'Ботсвана' },
  'Brazil': { en: 'Brazil', bg: 'Бразилия' },
  'Brunei': { en: 'Brunei', bg: 'Бруней' },
  'Bulgaria': { en: 'Bulgaria', bg: 'България' },
  'Burkina Faso': { en: 'Burkina Faso', bg: 'Буркина Фасо' },
  'Burundi': { en: 'Burundi', bg: 'Бурунди' },
  'Cabo Verde': { en: 'Cabo Verde', bg: 'Кабо Верде' },
  'Cambodia': { en: 'Cambodia', bg: 'Камбоджа' },
  'Cameroon': { en: 'Cameroon', bg: 'Камерун' },
  'Canada': { en: 'Canada', bg: 'Канада' },
  'Central African Republic': { en: 'Central African Republic', bg: 'Централноафриканска република' },
  'Chad': { en: 'Chad', bg: 'Чад' },
  'Chile': { en: 'Chile', bg: 'Чили' },
  'China': { en: 'China', bg: 'Китай' },
  'Colombia': { en: 'Colombia', bg: 'Колумбия' },
  'Comoros': { en: 'Comoros', bg: 'Коморски острови' },
  'Congo': { en: 'Congo', bg: 'Конго' },
  'Costa Rica': { en: 'Costa Rica', bg: 'Коста Рика' },
  'Croatia': { en: 'Croatia', bg: 'Хърватия' },
  'Cuba': { en: 'Cuba', bg: 'Куба' },
  'Cyprus': { en: 'Cyprus', bg: 'Кипър' },
  'Czech Republic': { en: 'Czech Republic', bg: 'Чехия' },
  'Democratic Republic of the Congo': { en: 'Democratic Republic of the Congo', bg: 'Демократична република Конго' },
  'Denmark': { en: 'Denmark', bg: 'Дания' },
  'Djibouti': { en: 'Djibouti', bg: 'Джибути' },
  'Dominica': { en: 'Dominica', bg: 'Доминика' },
  'Dominican Republic': { en: 'Dominican Republic', bg: 'Доминиканска република' },
  'Ecuador': { en: 'Ecuador', bg: 'Еквадор' },
  'Egypt': { en: 'Egypt', bg: 'Египет' },
  'El Salvador': { en: 'El Salvador', bg: 'Ел Салвадор' },
  'Equatorial Guinea': { en: 'Equatorial Guinea', bg: 'Екваториална Гвинея' },
  'Eritrea': { en: 'Eritrea', bg: 'Еритрея' },
  'Estonia': { en: 'Estonia', bg: 'Естония' },
  'Eswatini': { en: 'Eswatini', bg: 'Есватини' },
  'Ethiopia': { en: 'Ethiopia', bg: 'Етиопия' },
  'Fiji': { en: 'Fiji', bg: 'Фиджи' },
  'Finland': { en: 'Finland', bg: 'Финландия' },
  'France': { en: 'France', bg: 'Франция' },
  'Gabon': { en: 'Gabon', bg: 'Габон' },
  'Gambia': { en: 'Gambia', bg: 'Гамбия' },
  'Georgia': { en: 'Georgia', bg: 'Грузия' },
  'Germany': { en: 'Germany', bg: 'Германия' },
  'Ghana': { en: 'Ghana', bg: 'Гана' },
  'Greece': { en: 'Greece', bg: 'Гърция' },
  'Grenada': { en: 'Grenada', bg: 'Гренада' },
  'Guatemala': { en: 'Guatemala', bg: 'Гватемала' },
  'Guinea': { en: 'Guinea', bg: 'Гвинея' },
  'Guinea-Bissau': { en: 'Guinea-Bissau', bg: 'Гвинея-Бисау' },
  'Guyana': { en: 'Guyana', bg: 'Гвиана' },
  'Haiti': { en: 'Haiti', bg: 'Хаити' },
  'Honduras': { en: 'Honduras', bg: 'Хондурас' },
  'Hungary': { en: 'Hungary', bg: 'Унгария' },
  'Iceland': { en: 'Iceland', bg: 'Исландия' },
  'India': { en: 'India', bg: 'Индия' },
  'Indonesia': { en: 'Indonesia', bg: 'Индонезия' },
  'Iran': { en: 'Iran', bg: 'Иран' },
  'Iraq': { en: 'Iraq', bg: 'Ирак' },
  'Ireland': { en: 'Ireland', bg: 'Ирландия' },
  'Israel': { en: 'Israel', bg: 'Израел' },
  'Italy': { en: 'Italy', bg: 'Италия' },
  'Jamaica': { en: 'Jamaica', bg: 'Ямайка' },
  'Japan': { en: 'Japan', bg: 'Япония' },
  'Jordan': { en: 'Jordan', bg: 'Йордания' },
  'Kazakhstan': { en: 'Kazakhstan', bg: 'Казахстан' },
  'Kenya': { en: 'Kenya', bg: 'Кения' },
  'Kiribati': { en: 'Kiribati', bg: 'Кирибати' },
  'Kuwait': { en: 'Kuwait', bg: 'Кувейт' },
  'Kyrgyzstan': { en: 'Kyrgyzstan', bg: 'Киргизстан' },
  'Laos': { en: 'Laos', bg: 'Лаос' },
  'Latvia': { en: 'Latvia', bg: 'Латвия' },
  'Lebanon': { en: 'Lebanon', bg: 'Ливан' },
  'Lesotho': { en: 'Lesotho', bg: 'Лесото' },
  'Liberia': { en: 'Liberia', bg: 'Либерия' },
  'Libya': { en: 'Libya', bg: 'Либия' },
  'Liechtenstein': { en: 'Liechtenstein', bg: 'Лихтенщайн' },
  'Lithuania': { en: 'Lithuania', bg: 'Литва' },
  'Luxembourg': { en: 'Luxembourg', bg: 'Люксембург' },
  'Madagascar': { en: 'Madagascar', bg: 'Мадагаскар' },
  'Malawi': { en: 'Malawi', bg: 'Малави' },
  'Malaysia': { en: 'Malaysia', bg: 'Малайзия' },
  'Maldives': { en: 'Maldives', bg: 'Малдиви' },
  'Mali': { en: 'Mali', bg: 'Мали' },
  'Malta': { en: 'Malta', bg: 'Малта' },
  'Marshall Islands': { en: 'Marshall Islands', bg: 'Маршалови острови' },
  'Mauritania': { en: 'Mauritania', bg: 'Мавритания' },
  'Mauritius': { en: 'Mauritius', bg: 'Мавриций' },
  'Mexico': { en: 'Mexico', bg: 'Мексико' },
  'Micronesia': { en: 'Micronesia', bg: 'Микронезия' },
  'Moldova': { en: 'Moldova', bg: 'Молдова' },
  'Monaco': { en: 'Monaco', bg: 'Монако' },
  'Mongolia': { en: 'Mongolia', bg: 'Монголия' },
  'Montenegro': { en: 'Montenegro', bg: 'Черна гора' },
  'Morocco': { en: 'Morocco', bg: 'Мароко' },
  'Mozambique': { en: 'Mozambique', bg: 'Мозамбик' },
  'Myanmar': { en: 'Myanmar', bg: 'Мианмар' },
  'Namibia': { en: 'Namibia', bg: 'Намибия' },
  'Nauru': { en: 'Nauru', bg: 'Науру' },
  'Nepal': { en: 'Nepal', bg: 'Непал' },
  'Netherlands': { en: 'Netherlands', bg: 'Нидерландия' },
  'New Zealand': { en: 'New Zealand', bg: 'Нова Зеландия' },
  'Nicaragua': { en: 'Nicaragua', bg: 'Никарагуа' },
  'Niger': { en: 'Niger', bg: 'Нигер' },
  'Nigeria': { en: 'Nigeria', bg: 'Нигерия' },
  'North Korea': { en: 'North Korea', bg: 'Северна Корея' },
  'North Macedonia': { en: 'North Macedonia', bg: 'Северна Македония' },
  'Norway': { en: 'Norway', bg: 'Норвегия' },
  'Oman': { en: 'Oman', bg: 'Оман' },
  'Pakistan': { en: 'Pakistan', bg: 'Пакистан' },
  'Palau': { en: 'Palau', bg: 'Палау' },
  'Palestine': { en: 'Palestine', bg: 'Палестина' },
  'Panama': { en: 'Panama', bg: 'Панама' },
  'Papua New Guinea': { en: 'Papua New Guinea', bg: 'Папуа Нова Гвинея' },
  'Paraguay': { en: 'Paraguay', bg: 'Парагвай' },
  'Peru': { en: 'Peru', bg: 'Перу' },
  'Philippines': { en: 'Philippines', bg: 'Филипини' },
  'Poland': { en: 'Poland', bg: 'Полша' },
  'Portugal': { en: 'Portugal', bg: 'Португалия' },
  'Qatar': { en: 'Qatar', bg: 'Катар' },
  'Romania': { en: 'Romania', bg: 'Румъния' },
  'Russia': { en: 'Russia', bg: 'Русия' },
  'Rwanda': { en: 'Rwanda', bg: 'Руанда' },
  'Saint Kitts and Nevis': { en: 'Saint Kitts and Nevis', bg: 'Сейнт Китс и Невис' },
  'Saint Lucia': { en: 'Saint Lucia', bg: 'Сейнт Лусия' },
  'Saint Vincent and the Grenadines': { en: 'Saint Vincent and the Grenadines', bg: 'Сейнт Винсент и Гренадини' },
  'Samoa': { en: 'Samoa', bg: 'Самоа' },
  'San Marino': { en: 'San Marino', bg: 'Сан Марино' },
  'Sao Tome and Principe': { en: 'Sao Tome and Principe', bg: 'Сао Томе и Принсипи' },
  'Saudi Arabia': { en: 'Saudi Arabia', bg: 'Саудитска Арабия' },
  'Senegal': { en: 'Senegal', bg: 'Сенегал' },
  'Serbia': { en: 'Serbia', bg: 'Сърбия' },
  'Seychelles': { en: 'Seychelles', bg: 'Сейшели' },
  'Sierra Leone': { en: 'Sierra Leone', bg: 'Сиера Леоне' },
  'Singapore': { en: 'Singapore', bg: 'Сингапур' },
  'Slovakia': { en: 'Slovakia', bg: 'Словакия' },
  'Slovenia': { en: 'Slovenia', bg: 'Словения' },
  'Solomon Islands': { en: 'Solomon Islands', bg: 'Соломонови острови' },
  'Somalia': { en: 'Somalia', bg: 'Сомалия' },
  'South Africa': { en: 'South Africa', bg: 'Южна Африка' },
  'South Korea': { en: 'South Korea', bg: 'Южна Корея' },
  'South Sudan': { en: 'South Sudan', bg: 'Южен Судан' },
  'Spain': { en: 'Spain', bg: 'Испания' },
  'Sri Lanka': { en: 'Sri Lanka', bg: 'Шри Ланка' },
  'Sudan': { en: 'Sudan', bg: 'Судан' },
  'Suriname': { en: 'Suriname', bg: 'Суринам' },
  'Sweden': { en: 'Sweden', bg: 'Швеция' },
  'Switzerland': { en: 'Switzerland', bg: 'Швейцария' },
  'Syria': { en: 'Syria', bg: 'Сирия' },
  'Taiwan': { en: 'Taiwan', bg: 'Тайван' },
  'Tajikistan': { en: 'Tajikistan', bg: 'Таджикистан' },
  'Tanzania': { en: 'Tanzania', bg: 'Танзания' },
  'Thailand': { en: 'Thailand', bg: 'Тайланд' },
  'Timor-Leste': { en: 'Timor-Leste', bg: 'Източен Тимор' },
  'Togo': { en: 'Togo', bg: 'Того' },
  'Tonga': { en: 'Tonga', bg: 'Тонга' },
  'Trinidad and Tobago': { en: 'Trinidad and Tobago', bg: 'Тринидад и Тобаго' },
  'Tunisia': { en: 'Tunisia', bg: 'Тунис' },
  'Turkey': { en: 'Turkey', bg: 'Турция' },
  'Turkmenistan': { en: 'Turkmenistan', bg: 'Туркменистан' },
  'Tuvalu': { en: 'Tuvalu', bg: 'Тувалу' },
  'Uganda': { en: 'Uganda', bg: 'Уганда' },
  'Ukraine': { en: 'Ukraine', bg: 'Украйна' },
  'United Arab Emirates': { en: 'United Arab Emirates', bg: 'Обединени арабски емирства' },
  'United Kingdom': { en: 'United Kingdom', bg: 'Обединено кралство' },
  'United States': { en: 'United States', bg: 'Съединени щати' },
  'Uruguay': { en: 'Uruguay', bg: 'Уругвай' },
  'Uzbekistan': { en: 'Uzbekistan', bg: 'Узбекистан' },
  'Vanuatu': { en: 'Vanuatu', bg: 'Вануату' },
  'Vatican City': { en: 'Vatican City', bg: 'Ватикан' },
  'Venezuela': { en: 'Venezuela', bg: 'Венецуела' },
  'Vietnam': { en: 'Vietnam', bg: 'Виетнам' },
  'Yemen': { en: 'Yemen', bg: 'Йемен' },
  'Zambia': { en: 'Zambia', bg: 'Замбия' },
  'Zimbabwe': { en: 'Zimbabwe', bg: 'Зимбабве' }
};

/**
 * Get country mapping for a given country name
 */
export function getCountryMapping(countryName: string): CountryMapping {
  return COUNTRY_MAPPINGS[countryName] || { en: countryName, bg: countryName };
}

/**
 * Get all country names in English (for dropdown options)
 */
export function getAllCountryNames(): string[] {
  return Object.keys(COUNTRY_MAPPINGS).sort();
}
