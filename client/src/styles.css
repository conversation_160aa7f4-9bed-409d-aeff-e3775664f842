@import "tailwindcss";
@import "primeicons/primeicons.css";

/* Force light mode for form elements only */
html, body {
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  color-scheme: light;
}

/* Override dark mode only for form elements that have issues */
@media (prefers-color-scheme: dark) {
  /* Only target form elements that become unreadable */
  input,
  select,
  textarea,
  .p-inputtext,
  .p-select,
  .p-select-overlay,
  .p-select-option {
    color-scheme: light !important;
    background-color: white !important;
    color: #374151 !important;
  }
}

:root {
  color-scheme: light !important;
  --p-primary-color: #635bff;
  --p-primary-hover-color: #5046e5;
  --p-primary-active-color: #392dbf;
  --p-primary-contrast-color: #ffffff;

  --p-form-field-border-color: #e0e0e0;
  --p-form-field-border-radius: 6px;
  --p-form-field-focus-border-color: #635bff;

  --p-button-border-radius: 6px;
}
