@import "tailwindcss";
@import "primeicons/primeicons.css";

/* Force light mode globally and disable OS dark theme */
html, body {
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  color-scheme: light !important;
  background-color: white !important;
  color: #374151 !important;
}

/* Disable dark mode for all elements */
* {
  color-scheme: light !important;
}

/* Override dark mode media query globally */
@media (prefers-color-scheme: dark) {
  :root,
  html,
  body,
  * {
    color-scheme: light !important;
    background-color: white !important;
    color: #374151 !important;
  }

  /* Ensure PrimeNG components stay light */
  .p-component,
  .p-component * {
    color-scheme: light !important;
    background-color: white !important;
    color: #374151 !important;
  }
}

:root {
  color-scheme: light !important;
  --p-primary-color: #635bff;
  --p-primary-hover-color: #5046e5;
  --p-primary-active-color: #392dbf;
  --p-primary-contrast-color: #ffffff;

  --p-form-field-border-color: #e0e0e0;
  --p-form-field-border-radius: 6px;
  --p-form-field-focus-border-color: #635bff;

  --p-button-border-radius: 6px;
}
