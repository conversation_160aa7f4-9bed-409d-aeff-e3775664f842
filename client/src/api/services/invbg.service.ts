import axios from 'axios';

// v3 API interfaces based on inv.bg v3 specification
export interface InvBgV3Client {
  name: string;
  address: string;
  city: string;
  country: string;
  eik?: string;
  mol?: string;
  vat_number?: string;
  is_person: boolean;
  email?: string;
  phone?: string;
}

export interface InvBgV3InvoiceItem {
  name: string;
  quantity: number;
  price: number;
  unit: string;
  vat_percent: number;
  discount_percent?: number;
}

export interface InvBgV3InvoiceRequest {
  client: InvBgV3Client;
  items: InvBgV3InvoiceItem[];
  currency: string;
  date: string;
  due_date: string;
  invoice_number?: string;
  notes?: string;
  language: 'bg' | 'en';
  payment_method?: string;
  bank_account?: string;
}

export interface InvBgV3InvoiceResponse {
  id: number;
  invoice_number: string;
  status: string;
  pdf_url: string;
  total: number;
  currency: string;
  date: string;
  due_date: string;
  client: InvBgV3Client;
}

export interface InvBgV3AuthResponse {
  token: string;
  expires_at: string;
}

export class InvBgService {
  private readonly apiKey: string;
  private readonly baseUrl = 'https://inv.bg/api/v3';

  constructor() {
    this.apiKey = process.env['INVBG_API_KEY'] || '';

    if (!this.apiKey) {
      console.warn('INVBG_API_KEY not configured');
    }
  }

  async createInvoice(
    invoiceData: InvBgInvoiceRequest,
  ): Promise<InvBgInvoiceResponse> {
    try {
      const response = await axios.post(
        `${this.baseUrl}/invoices`,
        invoiceData,
        {
          headers: {
            Authorization: `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
        },
      );

      return response.data;
    } catch (error: any) {
      console.error('inv.bg API error:', error.response?.data || error.message);
      throw new Error(
        `Failed to create invoice: ${error.response?.data?.message || error.message}`,
      );
    }
  }

  async getInvoice(invoiceId: string): Promise<InvBgInvoiceResponse> {
    try {
      const response = await axios.get(
        `${this.baseUrl}/invoices/${invoiceId}`,
        {
          headers: {
            Authorization: `Bearer ${this.apiKey}`,
          },
        },
      );

      return response.data;
    } catch (error: any) {
      console.error('inv.bg API error:', error.response?.data || error.message);
      throw new Error(
        `Failed to get invoice: ${error.response?.data?.message || error.message}`,
      );
    }
  }

  async sendInvoice(invoiceId: string, email: string): Promise<void> {
    try {
      await axios.post(
        `${this.baseUrl}/invoices/${invoiceId}/send`,
        { email },
        {
          headers: {
            Authorization: `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
        },
      );
    } catch (error: any) {
      console.error('inv.bg API error:', error.response?.data || error.message);
      throw new Error(
        `Failed to send invoice: ${error.response?.data?.message || error.message}`,
      );
    }
  }
}
