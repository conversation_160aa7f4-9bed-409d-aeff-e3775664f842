import axios from 'axios';

// v3 API interfaces based on inv.bg v3 specification
export interface InvBgV3Client {
  name: string;
  address: string;
  town: string;
  country: string;
  eik?: string;
  mol?: string;
  is_reg_vat: boolean;
  vat_number?: string;
  is_person: boolean;
  bulstat: string | null;
  email?: string;
  phone?: string;
}

export interface InvBgV3InvoiceItem {
  name: string;
  quantity: number;
  price: number;
  unit: string;
  vat_percent: number;
  discount_percent?: number;
}

export interface InvBgV3InvoiceRequest {
  client_id: number;
  items: InvBgV3InvoiceItem[];
  currency: string;
  date: string;
  due_date: string;
  notes?: string;
  language: 'bg' | 'en';
  payment_method?: string;
  bank_account?: string;
}

export interface InvBgV3ClientResponse {
  id: number;
  name: string;
  address: string;
  city: string;
  country: string;
  eik?: string;
  mol?: string;
  vat_number?: string;
  is_person: boolean;
  email?: string;
  phone?: string;
}

export interface InvBgV3InvoiceResponse {
  id: number;
  invoice_number: string;
  status: string;
  pdf_url: string;
  total: number;
  currency: string;
  date: string;
  due_date: string;
  client: InvBgV3Client;
}

export class InvBgService {
  private readonly baseUrl = 'https://api.inv.bg/v3';
  private readonly apiKey: string;

  constructor() {
    this.apiKey = process.env['INVBG_API_KEY'] || '';

    if (!this.apiKey) {
      console.warn(
        'inv.bg v3 API key not configured. Need INVBG_API_KEY (JWT token)',
      );
    }
  }

  async createClient(
    clientData: InvBgV3Client,
  ): Promise<InvBgV3ClientResponse> {
    try {
      const response = await axios.post(`${this.baseUrl}/clients`, clientData, {
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
      });

      return response.data;
    } catch (error: any) {
      console.error('inv.bg API error:', error.response?.data || error.message);
      throw new Error(
        `Failed to create client: ${error.response?.data?.message || error.message}`,
      );
    }
  }

  async getClient(clientId: number): Promise<InvBgV3ClientResponse> {
    try {
      const response = await axios.get(`${this.baseUrl}/clients/${clientId}`, {
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
        },
      });

      return response.data;
    } catch (error: any) {
      console.error('inv.bg API error:', error.response?.data || error.message);
      throw new Error(
        `Failed to get client: ${error.response?.data?.message || error.message}`,
      );
    }
  }

  async createInvoice(
    invoiceData: InvBgV3InvoiceRequest,
  ): Promise<InvBgV3InvoiceResponse> {
    try {
      const response = await axios.post(
        `${this.baseUrl}/invoices`,
        invoiceData,
        {
          headers: {
            Authorization: `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
        },
      );

      return response.data;
    } catch (error: any) {
      console.error('inv.bg API error:', error.response?.data || error.message);
      throw new Error(
        `Failed to create invoice: ${error.response?.data?.message || error.message}`,
      );
    }
  }

  async getInvoice(invoiceId: number): Promise<InvBgV3InvoiceResponse> {
    try {
      const response = await axios.get(
        `${this.baseUrl}/invoices/${invoiceId}`,
        {
          headers: {
            Authorization: `Bearer ${this.apiKey}`,
          },
        },
      );

      return response.data;
    } catch (error: any) {
      console.error('inv.bg API error:', error.response?.data || error.message);
      throw new Error(
        `Failed to get invoice: ${error.response?.data?.message || error.message}`,
      );
    }
  }

  async sendInvoice(invoiceId: number, email: string): Promise<void> {
    try {
      await axios.post(
        `${this.baseUrl}/invoices/${invoiceId}/send`,
        { email },
        {
          headers: {
            Authorization: `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
        },
      );
    } catch (error: any) {
      console.error('inv.bg API error:', error.response?.data || error.message);
      throw new Error(
        `Failed to send invoice: ${error.response?.data?.message || error.message}`,
      );
    }
  }
}
