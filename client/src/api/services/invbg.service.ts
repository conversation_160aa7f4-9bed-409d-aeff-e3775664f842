import axios from 'axios';

export interface InvBgClient {
  name: string;
  address: string;
  city: string;
  country: string;
  eik?: string;
  mol?: string;
  vat_number?: string;
  is_person: boolean;
}

export interface InvBgInvoiceItem {
  name: string;
  quantity: number;
  price: number;
  unit: string;
  vat_percent: number;
}

export interface InvBgInvoiceRequest {
  client: InvBgClient;
  items: InvBgInvoiceItem[];
  currency: string;
  date: string;
  due_date: string;
  invoice_number?: string;
  notes?: string;
  language: 'bg' | 'en';
}

export interface InvBgInvoiceResponse {
  id: string;
  invoice_number: string;
  status: string;
  pdf_url: string;
  total: number;
  currency: string;
}

export class InvBgService {
  private apiKey: string;
  private baseUrl: string;

  constructor() {
    this.apiKey = process.env.INVBG_API_KEY || '';
    this.baseUrl = process.env.INVBG_BASE_URL || 'https://inv.bg/api/v1';
    
    if (!this.apiKey) {
      console.warn('INVBG_API_KEY not configured');
    }
  }

  async createInvoice(invoiceData: InvBgInvoiceRequest): Promise<InvBgInvoiceResponse> {
    try {
      const response = await axios.post(
        `${this.baseUrl}/invoices`,
        invoiceData,
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
        }
      );

      return response.data;
    } catch (error: any) {
      console.error('inv.bg API error:', error.response?.data || error.message);
      throw new Error(`Failed to create invoice: ${error.response?.data?.message || error.message}`);
    }
  }

  async getInvoice(invoiceId: string): Promise<InvBgInvoiceResponse> {
    try {
      const response = await axios.get(
        `${this.baseUrl}/invoices/${invoiceId}`,
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
          },
        }
      );

      return response.data;
    } catch (error: any) {
      console.error('inv.bg API error:', error.response?.data || error.message);
      throw new Error(`Failed to get invoice: ${error.response?.data?.message || error.message}`);
    }
  }

  async sendInvoice(invoiceId: string, email: string): Promise<void> {
    try {
      await axios.post(
        `${this.baseUrl}/invoices/${invoiceId}/send`,
        { email },
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
        }
      );
    } catch (error: any) {
      console.error('inv.bg API error:', error.response?.data || error.message);
      throw new Error(`Failed to send invoice: ${error.response?.data?.message || error.message}`);
    }
  }
}
