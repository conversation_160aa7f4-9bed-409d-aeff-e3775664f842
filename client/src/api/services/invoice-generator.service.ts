import { InvBgService, InvBgV3InvoiceRequest, InvBgV3Client, InvBgV3InvoiceItem } from './invbg.service';
import { getSupabaseClient } from './supabase.service';

export interface ProposalWithClient {
  id: string;
  proposal_id: string;
  project_title: string;
  price: number;
  currency: string;
  status: 'draft' | 'sent' | 'accepted' | 'partially_paid' | 'paid';
  paid_amount?: number;
  initial_invoice_id?: string;
  final_invoice_id?: string;
  client: {
    id: string;
    name: string;
    email: string;
    rep_full_name: string;
    billing_information?: {
      company_name: string;
      address: string;
      city: string;
      country: string;
      eik?: string;
      mol?: string;
      vat_number?: string;
      is_bulgarian: boolean;
    } | null;
  };
}

export class InvoiceGeneratorService {
  private invBgService: InvBgService;

  constructor() {
    this.invBgService = new InvBgService();
  }

  /**
   * Pure function to determine if an invoice should be created
   */
  shouldCreateInvoice(proposal: ProposalWithClient): {
    shouldCreate: boolean;
    invoiceType: 'initial' | 'final' | null;
    reason?: string;
  } {
    // Check if client has billing information
    if (!proposal.client.billing_information) {
      return {
        shouldCreate: false,
        invoiceType: null,
        reason: 'No billing information available'
      };
    }

    // Check if client is Bulgarian (inv.bg is primarily for Bulgarian invoices)
    if (!proposal.client.billing_information.is_bulgarian) {
      return {
        shouldCreate: false,
        invoiceType: null,
        reason: 'Client is not Bulgarian - inv.bg is for Bulgarian invoices'
      };
    }

    // Determine invoice type based on status
    if (proposal.status === 'partially_paid' && !proposal.initial_invoice_id) {
      return {
        shouldCreate: true,
        invoiceType: 'initial'
      };
    }

    if (proposal.status === 'paid' && !proposal.final_invoice_id) {
      return {
        shouldCreate: true,
        invoiceType: 'final'
      };
    }

    return {
      shouldCreate: false,
      invoiceType: null,
      reason: 'Invoice already exists or status does not require invoice'
    };
  }

  /**
   * Pure function to build inv.bg invoice data from proposal
   */
  buildInvoiceData(proposal: ProposalWithClient, invoiceType: 'initial' | 'final'): InvBgInvoiceRequest {
    const billing = proposal.client.billing_information!;

    // Build client data
    const client: InvBgClient = {
      name: billing.company_name,
      address: billing.address,
      city: billing.city,
      country: billing.country,
      eik: billing.eik || undefined,
      mol: billing.mol || undefined,
      vat_number: billing.vat_number || undefined,
      is_person: false // Company invoice
    };

    // Calculate amount based on invoice type
    const amount = invoiceType === 'initial'
      ? (proposal.paid_amount || 0)
      : proposal.price;

    // Build invoice items
    const items: InvBgInvoiceItem[] = [
      {
        name: `${proposal.project_title} - ${invoiceType === 'initial' ? 'Частично плащане' : 'Окончателно плащане'}`,
        quantity: 1,
        price: amount,
        unit: 'бр.',
        vat_percent: 20 // Standard Bulgarian VAT rate
      }
    ];

    // Calculate due date (30 days from now)
    const dueDate = new Date();
    dueDate.setDate(dueDate.getDate() + 30);

    return {
      client,
      items,
      currency: proposal.currency,
      date: new Date().toISOString().split('T')[0],
      due_date: dueDate.toISOString().split('T')[0],
      invoice_number: `${proposal.proposal_id}-${invoiceType.toUpperCase()}`,
      notes: `Фактура за проект: ${proposal.project_title}`,
      language: 'bg'
    };
  }

  /**
   * Main function to generate invoice for a proposal
   */
  async generateInvoiceForProposal(proposalId: string): Promise<{
    success: boolean;
    invoiceId?: string;
    invoiceType?: 'initial' | 'final';
    message: string;
  }> {
    try {
      // Fetch proposal with client and billing information
      const { data: proposal, error } = await getSupabaseClient()
        .from('proposals')
        .select(`
          *,
          client:clients (
            id,
            name,
            email,
            rep_full_name,
            billing_information (*)
          )
        `)
        .eq('id', proposalId)
        .single();

      if (error || !proposal) {
        return {
          success: false,
          message: 'Proposal not found'
        };
      }

      // Transform billing information (it comes as array from Supabase)
      const transformedProposal: ProposalWithClient = {
        ...proposal,
        client: {
          ...proposal.client,
          billing_information: proposal.client.billing_information?.[0] || null
        }
      };

      // Check if invoice should be created
      const shouldCreate = this.shouldCreateInvoice(transformedProposal);

      if (!shouldCreate.shouldCreate) {
        return {
          success: false,
          message: shouldCreate.reason || 'Invoice creation not needed'
        };
      }

      // Build invoice data
      const invoiceData = this.buildInvoiceData(transformedProposal, shouldCreate.invoiceType!);

      // Create invoice via inv.bg API
      const invoiceResponse = await this.invBgService.createInvoice(invoiceData);

      // Update proposal with invoice ID
      const updateField = shouldCreate.invoiceType === 'initial'
        ? 'initial_invoice_id'
        : 'final_invoice_id';

      const { error: updateError } = await getSupabaseClient()
        .from('proposals')
        .update({ [updateField]: invoiceResponse.id })
        .eq('id', proposalId);

      if (updateError) {
        console.error('Failed to update proposal with invoice ID:', updateError);
        // Invoice was created but we couldn't save the ID - log this for manual handling
      }

      // Send invoice to client email
      try {
        await this.invBgService.sendInvoice(invoiceResponse.id, transformedProposal.client.email);
      } catch (emailError) {
        console.error('Failed to send invoice email:', emailError);
        // Don't fail the whole process if email sending fails
      }

      return {
        success: true,
        invoiceId: invoiceResponse.id,
        invoiceType: shouldCreate.invoiceType!,
        message: `${shouldCreate.invoiceType === 'initial' ? 'Initial' : 'Final'} invoice created successfully`
      };

    } catch (error: any) {
      console.error('Invoice generation error:', error);
      return {
        success: false,
        message: `Failed to generate invoice: ${error.message}`
      };
    }
  }
}
