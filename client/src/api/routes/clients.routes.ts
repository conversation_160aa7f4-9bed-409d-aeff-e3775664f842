import { Router } from 'express';
import { requireAuth } from '../middlewares';
import { getSupabaseClient, validateRequest } from '../services';
import { ClientRequestSchema, BillingInformationRequestSchema } from '../../lib/validators';

const router = Router();

router.get('/', requireAuth, async (req, res) => {
  const { data, error } = await getSupabaseClient()
    .from('clients')
    .select(`
      *,
      billing_information (*)
    `)
    .order('created_at', { ascending: false });

  if (error) {
    console.error(error);
    return res.status(500).json({ error: 'Failed to fetch clients' });
  }

  // Transform the data to match our Client interface
  const transformedData = data?.map(client => ({
    ...client,
    billing_information: client.billing_information?.[0] || null
  }));

  return res.json(transformedData);
});

router.post('/', requireAuth, async (req, res) => {
  const validation = validateRequest(ClientRequestSchema, req);

  if (!validation.success) {
    return res.status(400).json({
      error: validation.error,
      details: validation.details,
    });
  }

  const { name, email, rep_full_name } = validation.data;

  const { data, error } = await getSupabaseClient()
    .from('clients')
    .insert([{ name, email, rep_full_name }])
    .select()
    .single();

  if (error) {
    console.error(error);
    return res.status(500).json({ error: 'Failed to create client' });
  }

  return res.json(data);
});

router.put('/:id', requireAuth, async (req, res) => {
  const { id } = req.params;

  if (!id) {
    return res.status(400).json({ error: 'Missing client ID' });
  }

  const validation = validateRequest(ClientRequestSchema, req);

  if (!validation.success) {
    return res.status(400).json({
      error: validation.error,
      details: validation.details,
    });
  }

  const { data, error } = await getSupabaseClient()
    .from('clients')
    .update(validation.data)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    console.error(error);
    return res.status(500).json({ error: 'Failed to update client' });
  }

  return res.json(data);
});

router.delete('/:id', requireAuth, async (req, res) => {
  const { id } = req.params;

  if (!id) {
    return res.status(400).json({ error: 'Missing client ID' });
  }

  const { error } = await getSupabaseClient()
    .from('clients')
    .delete()
    .eq('id', id);

  if (error) {
    console.error(error);
  }

  return res.json({ success: !error });
});

// Billing Information endpoints
router.get('/:id/billing', async (req, res) => {
  const { id } = req.params;

  if (!id) {
    return res.status(400).json({ error: 'Missing client ID' });
  }

  const { data, error } = await getSupabaseClient()
    .from('billing_information')
    .select('*')
    .eq('client_id', id)
    .single();

  if (error && error.code !== 'PGRST116') { // PGRST116 is "not found"
    console.error(error);
    return res.status(500).json({ error: 'Failed to fetch billing information' });
  }

  return res.json(data || null);
});

router.post('/:id/billing', async (req, res) => {
  const { id } = req.params;

  if (!id) {
    return res.status(400).json({ error: 'Missing client ID' });
  }

  const validation = validateRequest(BillingInformationRequestSchema, req);

  if (!validation.success) {
    return res.status(400).json({
      error: validation.error,
      details: validation.details,
    });
  }

  // Check if billing information already exists
  const { data: existing } = await getSupabaseClient()
    .from('billing_information')
    .select('id')
    .eq('client_id', id)
    .single();

  if (existing) {
    return res.status(400).json({ error: 'Billing information already exists for this client' });
  }

  const { data, error } = await getSupabaseClient()
    .from('billing_information')
    .insert([{ ...validation.data, client_id: id }])
    .select()
    .single();

  if (error) {
    console.error(error);
    return res.status(500).json({ error: 'Failed to create billing information' });
  }

  return res.json(data);
});

router.put('/:id/billing', async (req, res) => {
  const { id } = req.params;

  if (!id) {
    return res.status(400).json({ error: 'Missing client ID' });
  }

  const validation = validateRequest(BillingInformationRequestSchema, req);

  if (!validation.success) {
    return res.status(400).json({
      error: validation.error,
      details: validation.details,
    });
  }

  const { data, error } = await getSupabaseClient()
    .from('billing_information')
    .update(validation.data)
    .eq('client_id', id)
    .select()
    .single();

  if (error) {
    console.error(error);
    return res.status(500).json({ error: 'Failed to update billing information' });
  }

  return res.json(data);
});

export default router;
