import { Router } from 'express';
import { requireAuth } from '../middlewares';
import { getSupabaseClient, validateRequest } from '../services';
import { ClientRequestSchema } from '../../lib/validators';

const router = Router();

router.get('/', requireAuth, async (req, res) => {
  const { data, error } = await getSupabaseClient()
    .from('clients')
    .select(`
      *,
      billing_information (*)
    `)
    .order('created_at', { ascending: false });

  if (error) {
    console.error(error);
    return res.status(500).json({ error: 'Failed to fetch clients' });
  }

  // Transform the data to match our Client interface
  const transformedData = data?.map(client => ({
    ...client,
    billing_information: client.billing_information?.[0] || null
  }));

  return res.json(transformedData);
});

router.post('/', requireAuth, async (req, res) => {
  const validation = validateRequest(ClientRequestSchema, req);

  if (!validation.success) {
    return res.status(400).json({
      error: validation.error,
      details: validation.details,
    });
  }

  const { name, email, rep_full_name } = validation.data;

  const { data, error } = await getSupabaseClient()
    .from('clients')
    .insert([{ name, email, rep_full_name }])
    .select()
    .single();

  if (error) {
    console.error(error);
    return res.status(500).json({ error: 'Failed to create client' });
  }

  return res.json(data);
});

router.put('/:id', requireAuth, async (req, res) => {
  const { id } = req.params;

  if (!id) {
    return res.status(400).json({ error: 'Missing client ID' });
  }

  const validation = validateRequest(ClientRequestSchema, req);

  if (!validation.success) {
    return res.status(400).json({
      error: validation.error,
      details: validation.details,
    });
  }

  const { data, error } = await getSupabaseClient()
    .from('clients')
    .update(validation.data)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    console.error(error);
    return res.status(500).json({ error: 'Failed to update client' });
  }

  return res.json(data);
});

router.delete('/:id', requireAuth, async (req, res) => {
  const { id } = req.params;

  if (!id) {
    return res.status(400).json({ error: 'Missing client ID' });
  }

  const { error } = await getSupabaseClient()
    .from('clients')
    .delete()
    .eq('id', id);

  if (error) {
    console.error(error);
  }

  return res.json({ success: !error });
});

export default router;
