h1,
h2 {
  font-family: "Syne";
}

/* Custom spinner styling to match the purple theme */
::ng-deep .custom-spinner .p-progress-spinner-circle {
  stroke: #a855f7; /* Purple-500 color to match the button gradient */
  animation: p-progress-spinner-dash 1.5s ease-in-out infinite;
}

::ng-deep .custom-spinner .p-progress-spinner-svg {
  animation: p-progress-spinner-rotate 2s linear infinite;
}

/* Enhanced section animations and effects */
.group:hover {
  transform: translateY(-2px);
}

/* Custom focus states for inputs */
::ng-deep .p-inputtext:focus,
::ng-deep .p-inputtextarea:focus {
  box-shadow: 0 0 0 2px rgba(168, 85, 247, 0.2);
  border-color: #a855f7 !important;
}

/* Smooth transitions for all interactive elements */
* {
  transition: all 0.2s ease-in-out;
}

/* Enhanced button hover effects */
::ng-deep .p-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Section header icon animations */
.bg-gradient-to-r:hover {
  transform: scale(1.05);
}

/* Backdrop blur enhancement for better visual depth */
.backdrop-blur-sm {
  backdrop-filter: blur(8px);
}

/* Custom scrollbar for textareas */
::ng-deep .p-inputtextarea::-webkit-scrollbar {
  width: 6px;
}

::ng-deep .p-inputtextarea::-webkit-scrollbar-track {
  background: rgba(71, 85, 105, 0.3);
  border-radius: 3px;
}

::ng-deep .p-inputtextarea::-webkit-scrollbar-thumb {
  background: rgba(168, 85, 247, 0.5);
  border-radius: 3px;
}

::ng-deep .p-inputtextarea::-webkit-scrollbar-thumb:hover {
  background: rgba(168, 85, 247, 0.7);
}

/* Custom Select Styling */
::ng-deep .custom-select .p-select {
  background: rgba(15, 23, 42, 0.5);
  border: 1px solid rgb(71, 85, 105);
  border-radius: 0.5rem;
  color: white;
}

::ng-deep .custom-select .p-select:hover {
  border-color: #6366f1;
}

::ng-deep .custom-select .p-select:focus {
  border-color: #6366f1;
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}

::ng-deep .custom-select .p-select-panel {
  background: rgba(30, 41, 59, 0.95);
  border: 1px solid rgba(71, 85, 105, 0.5);
  border-radius: 0.5rem;
  backdrop-filter: blur(8px);
}

::ng-deep .custom-select .p-select-option {
  color: #e2e8f0;
  padding: 0.75rem 1rem;
}

::ng-deep .custom-select .p-select-option:hover {
  background: rgba(99, 102, 241, 0.2);
  color: white;
}

::ng-deep .custom-select .p-select-option.p-selected {
  background: #6366f1;
  color: white;
}

/* Custom Calendar Styling */
::ng-deep .custom-calendar .p-calendar .p-inputtext {
  background: rgba(15, 23, 42, 0.5) !important;
  border: 1px solid rgb(71, 85, 105) !important;
  border-radius: 0.5rem !important;
  color: white !important;
}

::ng-deep .custom-calendar .p-calendar .p-inputtext:hover {
  border-color: #6366f1 !important;
}

::ng-deep .custom-calendar .p-calendar .p-inputtext:focus {
  border-color: #6366f1 !important;
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2) !important;
}

::ng-deep .custom-calendar .p-datepicker {
  background: rgba(30, 41, 59, 0.95);
  border: 1px solid rgba(71, 85, 105, 0.5);
  border-radius: 0.5rem;
  backdrop-filter: blur(8px);
}

::ng-deep .custom-calendar .p-datepicker .p-datepicker-header {
  background: rgba(51, 65, 85, 0.5);
  border-bottom: 1px solid rgba(71, 85, 105, 0.5);
  color: white;
}

::ng-deep .custom-calendar .p-datepicker .p-datepicker-calendar td > span {
  color: #e2e8f0;
}

::ng-deep .custom-calendar .p-datepicker .p-datepicker-calendar td > span:hover {
  background: rgba(99, 102, 241, 0.2);
  color: white;
}

::ng-deep .custom-calendar .p-datepicker .p-datepicker-calendar td > span.p-highlight {
  background: #6366f1;
  color: white;
}
