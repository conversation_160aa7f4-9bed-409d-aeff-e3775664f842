import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { CommonModule } from '@angular/common';
import { BillingFormComponent } from '../../components/billing-form/billing-form.component';
import { ProposalService, ClientService } from '../../services';
import { Proposal } from '../../../lib/models';
import { BillingInformationRequest } from '../../../lib/validators';
import { MessageService } from 'primeng/api';
import { Toast } from 'primeng/toast';

@Component({
  selector: 'chm-successful-payment',
  imports: [CommonModule, RouterLink, BillingFormComponent, Toast],
  templateUrl: './successful-payment.component.html',
  styleUrl: './successful-payment.component.css',
})
export class SuccessfulPaymentComponent implements OnInit {
  proposalId!: string;
  proposal: Proposal | null = null;
  showBillingForm = false;
  loading = false;
  billingFormLoading = false;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private proposalService: ProposalService,
    private clientService: ClientService,
    private messageService: MessageService
  ) {}

  ngOnInit() {
    this.route.queryParams.subscribe((params) => {
      this.proposalId = params['p'];
      if (this.proposalId) {
        this.loadProposal();
      }
    });
  }

  private loadProposal() {
    this.loading = true;
    this.proposalService.get(this.proposalId).subscribe({
      next: (proposal) => {
        this.proposal = proposal;
        this.checkBillingInformation();
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading proposal:', error);
        this.loading = false;
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to load proposal information'
        });
      }
    });
  }

  private checkBillingInformation() {
    if (this.proposal?.client && !this.proposal.client.billing_information) {
      this.showBillingForm = true;
    }
  }

  onBillingFormSubmit(billingData: BillingInformationRequest) {
    if (!this.proposal?.client?.id) return;

    this.billingFormLoading = true;
    this.clientService.createBillingInformation(this.proposal.client.id, billingData).subscribe({
      next: (billingInfo) => {
        this.billingFormLoading = false;
        this.showBillingForm = false;
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Billing information saved successfully! Your invoice will be generated shortly.'
        });

        // Update the proposal client with billing information
        if (this.proposal?.client) {
          this.proposal.client.billing_information = billingInfo;
        }
      },
      error: (error) => {
        console.error('Error saving billing information:', error);
        this.billingFormLoading = false;
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to save billing information. Please try again.'
        });
      }
    });
  }

  onBillingFormCancel() {
    this.showBillingForm = false;
    this.messageService.add({
      severity: 'info',
      summary: 'Skipped',
      detail: 'You can add billing information later from your proposal page.'
    });
  }
}
