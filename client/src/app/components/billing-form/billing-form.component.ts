import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { InputText } from 'primeng/inputtext';
import { Checkbox } from 'primeng/checkbox';
import { ProgressSpinner } from 'primeng/progressspinner';
import { BillingInformationRequest } from '../../../lib/validators';

@Component({
  selector: 'chm-billing-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    InputText,
    Checkbox,
    ProgressSpinner,
  ],
  templateUrl: './billing-form.component.html',
  styleUrl: './billing-form.component.css',
})
export class BillingFormComponent implements OnInit {
  @Input() loading = false;
  @Input() initialData: BillingInformationRequest | null = null;
  @Output() formSubmit = new EventEmitter<BillingInformationRequest>();
  @Output() cancel = new EventEmitter<void>();

  billingForm!: FormGroup;

  constructor(private fb: FormBuilder) {}

  ngOnInit() {
    this.billingForm = this.fb.group({
      company_name: [this.initialData?.company_name || '', Validators.required],
      vat_number: [this.initialData?.vat_number || ''],
      eik: [this.initialData?.eik || ''],
      mol: [this.initialData?.mol || ''],
      address: [this.initialData?.address || '', Validators.required],
      country: [this.initialData?.country || '', Validators.required],
      city: [this.initialData?.city || '', Validators.required],
      is_bulgarian: [this.initialData?.is_bulgarian || false],
    });
  }

  onSubmit() {
    if (this.billingForm.valid) {
      this.formSubmit.emit(this.billingForm.value);
    }
  }

  onCancel() {
    this.cancel.emit();
  }

  get isFormValid() {
    return this.billingForm.valid;
  }
}
