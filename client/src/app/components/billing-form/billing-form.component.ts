import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { InputText } from 'primeng/inputtext';
import { Select } from 'primeng/select';
import { ProgressSpinner } from 'primeng/progressspinner';
import { BillingInformationRequest } from '../../../lib/validators';

@Component({
  selector: 'chm-billing-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    InputText,
    Select,
    ProgressSpinner,
  ],
  templateUrl: './billing-form.component.html',
  styleUrl: './billing-form.component.css',
})
export class BillingFormComponent implements OnInit {
  @Input() loading = false;
  @Input() initialData: BillingInformationRequest | null = null;
  @Output() formSubmit = new EventEmitter<BillingInformationRequest>();

  billingForm!: FormGroup;
  showAllFields = false;
  isBulgarian = false;

  countries = [
    'Bulgaria',
    'United States',
    'United Kingdom',
    'Germany',
    'France',
    'Spain',
    'Italy',
    'Netherlands',
    'Belgium',
    'Austria',
    'Switzerland',
    'Canada',
    'Australia',
    'Other'
  ];

  constructor(private fb: FormBuilder) {}

  ngOnInit() {
    this.billingForm = this.fb.group({
      company_name: [this.initialData?.company_name || '', Validators.required],
      vat_number: [this.initialData?.vat_number || ''],
      eik: [this.initialData?.eik || ''],
      mol: [this.initialData?.mol || ''],
      address: [this.initialData?.address || '', Validators.required],
      country: [this.initialData?.country || '', Validators.required],
      city: [this.initialData?.city || '', Validators.required],
      is_bulgarian: [this.initialData?.is_bulgarian || false],
    });

    // Watch for country changes
    this.billingForm.get('country')?.valueChanges.subscribe(country => {
      this.isBulgarian = country === 'Bulgaria';
      this.billingForm.patchValue({ is_bulgarian: this.isBulgarian });

      if (country) {
        this.showAllFields = true;
      }
    });

    // If initial data has country, show all fields
    if (this.initialData?.country) {
      this.showAllFields = true;
      this.isBulgarian = this.initialData.country === 'Bulgaria';
    }
  }

  onSubmit() {
    if (this.billingForm.valid) {
      const formValue = this.billingForm.value;
      // Clear Bulgarian-specific fields if not Bulgarian
      if (!this.isBulgarian) {
        formValue.eik = '';
        formValue.mol = '';
      }
      this.formSubmit.emit(formValue);
    }
  }

  get isFormValid() {
    return this.billingForm.valid;
  }
}
