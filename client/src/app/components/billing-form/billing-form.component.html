<div class="bg-white rounded-xl shadow-lg p-8 max-w-2xl mx-auto border border-[#e5d6f0]">
  <div class="text-center mb-8">
    <h2 class="text-3xl font-bold text-[#7125bb] mb-2">Complete Your Billing Information</h2>
    <p class="text-gray-600">We need your billing details to generate your invoice</p>
  </div>

  <form [formGroup]="billingForm" (ngSubmit)="onSubmit()" class="space-y-6">
    <!-- Company Name -->
    <div>
      <label for="company_name" class="block text-sm font-medium text-[#7125bb] mb-2">
        Company Name *
      </label>
      <input
        id="company_name"
        type="text"
        formControlName="company_name"
        pInputText
        class="w-full px-4 py-3 border border-[#d7b4f4] rounded-lg focus:ring-2 focus:ring-[#7125bb] focus:border-transparent transition-all"
        placeholder="Enter your company name"
      />
      <div *ngIf="billingForm.get('company_name')?.invalid && billingForm.get('company_name')?.touched" 
           class="text-red-500 text-sm mt-1">
        Company name is required
      </div>
    </div>

    <!-- VAT Number -->
    <div>
      <label for="vat_number" class="block text-sm font-medium text-[#7125bb] mb-2">
        VAT Number
      </label>
      <input
        id="vat_number"
        type="text"
        formControlName="vat_number"
        pInputText
        class="w-full px-4 py-3 border border-[#d7b4f4] rounded-lg focus:ring-2 focus:ring-[#7125bb] focus:border-transparent transition-all"
        placeholder="Enter VAT number (optional)"
      />
    </div>

    <!-- EIK (Bulgarian companies) -->
    <div>
      <label for="eik" class="block text-sm font-medium text-[#7125bb] mb-2">
        EIK (Bulgarian companies)
      </label>
      <input
        id="eik"
        type="text"
        formControlName="eik"
        pInputText
        class="w-full px-4 py-3 border border-[#d7b4f4] rounded-lg focus:ring-2 focus:ring-[#7125bb] focus:border-transparent transition-all"
        placeholder="Enter EIK (optional)"
      />
    </div>

    <!-- MOL (Bulgarian companies) -->
    <div>
      <label for="mol" class="block text-sm font-medium text-[#7125bb] mb-2">
        MOL (Bulgarian companies)
      </label>
      <input
        id="mol"
        type="text"
        formControlName="mol"
        pInputText
        class="w-full px-4 py-3 border border-[#d7b4f4] rounded-lg focus:ring-2 focus:ring-[#7125bb] focus:border-transparent transition-all"
        placeholder="Enter MOL (optional)"
      />
    </div>

    <!-- Address -->
    <div>
      <label for="address" class="block text-sm font-medium text-[#7125bb] mb-2">
        Address *
      </label>
      <input
        id="address"
        type="text"
        formControlName="address"
        pInputText
        class="w-full px-4 py-3 border border-[#d7b4f4] rounded-lg focus:ring-2 focus:ring-[#7125bb] focus:border-transparent transition-all"
        placeholder="Enter your company address"
      />
      <div *ngIf="billingForm.get('address')?.invalid && billingForm.get('address')?.touched" 
           class="text-red-500 text-sm mt-1">
        Address is required
      </div>
    </div>

    <!-- Country -->
    <div>
      <label for="country" class="block text-sm font-medium text-[#7125bb] mb-2">
        Country *
      </label>
      <input
        id="country"
        type="text"
        formControlName="country"
        pInputText
        class="w-full px-4 py-3 border border-[#d7b4f4] rounded-lg focus:ring-2 focus:ring-[#7125bb] focus:border-transparent transition-all"
        placeholder="Enter your country"
      />
      <div *ngIf="billingForm.get('country')?.invalid && billingForm.get('country')?.touched" 
           class="text-red-500 text-sm mt-1">
        Country is required
      </div>
    </div>

    <!-- City -->
    <div>
      <label for="city" class="block text-sm font-medium text-[#7125bb] mb-2">
        City *
      </label>
      <input
        id="city"
        type="text"
        formControlName="city"
        pInputText
        class="w-full px-4 py-3 border border-[#d7b4f4] rounded-lg focus:ring-2 focus:ring-[#7125bb] focus:border-transparent transition-all"
        placeholder="Enter your city"
      />
      <div *ngIf="billingForm.get('city')?.invalid && billingForm.get('city')?.touched" 
           class="text-red-500 text-sm mt-1">
        City is required
      </div>
    </div>

    <!-- Is Bulgarian -->
    <div class="flex items-center space-x-3">
      <p-checkbox
        formControlName="is_bulgarian"
        [binary]="true"
        inputId="is_bulgarian"
      ></p-checkbox>
      <label for="is_bulgarian" class="text-sm font-medium text-[#7125bb]">
        This is a Bulgarian company
      </label>
    </div>

    <!-- Action Buttons -->
    <div class="flex flex-col sm:flex-row gap-4 pt-6">
      <button
        type="button"
        (click)="onCancel()"
        [disabled]="loading"
        class="flex-1 px-6 py-3 border border-[#d7b4f4] text-[#7125bb] font-semibold rounded-lg hover:bg-[#f5f0ff] transition-colors disabled:opacity-50"
      >
        Skip for Now
      </button>
      <button
        type="submit"
        [disabled]="!isFormValid || loading"
        class="flex-1 px-6 py-3 bg-gradient-to-r from-[#d734b1] to-[#7125bb] text-white font-semibold rounded-lg shadow hover:from-[#7125bb] hover:to-[#d734b1] transition-colors disabled:opacity-50 flex items-center justify-center"
      >
        <p-progressSpinner
          *ngIf="loading"
          styleClass="w-5 h-5 mr-2"
          strokeWidth="4"
        ></p-progressSpinner>
        {{ loading ? 'Saving...' : 'Save Billing Information' }}
      </button>
    </div>
  </form>
</div>
