<div class="bg-white rounded-xl shadow-lg p-8 max-w-2xl mx-auto border border-[#e5d6f0]">
  <div class="text-center mb-8">
    <h2 class="text-3xl font-bold text-[#7125bb] mb-2">Complete Your Billing Information</h2>
    <p class="text-gray-600">We need your billing details to generate your invoice</p>
  </div>

  <form [formGroup]="billingForm" (ngSubmit)="onSubmit()" class="space-y-6">

    <!-- Step 1: Country Selection -->
    <div class="space-y-4">
      <div>
        <label for="country" class="block text-sm font-medium text-[#7125bb] mb-2">
          Country *
        </label>
        <p-select
          formControlName="country"
          [options]="countries"
          placeholder="Select your country"
          styleClass="w-full"
          [style]="{'width': '100%'}"
          inputId="country"
        ></p-select>
        <div *ngIf="billingForm.get('country')?.invalid && billingForm.get('country')?.touched"
             class="text-red-500 text-sm mt-1">
          Country is required
        </div>
      </div>
    </div>

    <!-- Step 2: All Other Fields (shown after country selection) -->
    <div *ngIf="showAllFields" class="space-y-6 animate-fade-in">

      <!-- Company Name -->
      <div>
        <label for="company_name" class="block text-sm font-medium text-[#7125bb] mb-2">
          Company Name *
        </label>
        <input
          id="company_name"
          type="text"
          formControlName="company_name"
          pInputText
          class="w-full"
          placeholder="Enter your company name"
        />
        <div *ngIf="billingForm.get('company_name')?.invalid && billingForm.get('company_name')?.touched"
             class="text-red-500 text-sm mt-1">
          Company name is required
        </div>
      </div>

      <!-- Address -->
      <div>
        <label for="address" class="block text-sm font-medium text-[#7125bb] mb-2">
          Address *
        </label>
        <input
          id="address"
          type="text"
          formControlName="address"
          pInputText
          class="w-full"
          placeholder="Enter your company address"
        />
        <div *ngIf="billingForm.get('address')?.invalid && billingForm.get('address')?.touched"
             class="text-red-500 text-sm mt-1">
          Address is required
        </div>
      </div>

      <!-- City -->
      <div>
        <label for="city" class="block text-sm font-medium text-[#7125bb] mb-2">
          City *
        </label>
        <input
          id="city"
          type="text"
          formControlName="city"
          pInputText
          class="w-full"
          placeholder="Enter your city"
        />
        <div *ngIf="billingForm.get('city')?.invalid && billingForm.get('city')?.touched"
             class="text-red-500 text-sm mt-1">
          City is required
        </div>
      </div>

      <!-- VAT Number -->
      <div>
        <label for="vat_number" class="block text-sm font-medium text-[#7125bb] mb-2">
          VAT Number
        </label>
        <input
          id="vat_number"
          type="text"
          formControlName="vat_number"
          pInputText
          class="w-full"
          placeholder="Enter VAT number (optional)"
        />
      </div>

      <!-- Bulgarian-specific fields -->
      <div *ngIf="isBulgarian" class="space-y-6 bg-blue-50 p-6 rounded-lg border border-blue-200 animate-fade-in">
        <div class="flex items-center mb-4">
          <svg class="w-5 h-5 text-blue-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
          </svg>
          <span class="text-blue-800 font-medium">Bulgarian Company Information</span>
        </div>

        <!-- EIK -->
        <div>
          <label for="eik" class="block text-sm font-medium text-blue-700 mb-2">
            EIK (Единен идентификационен код)
          </label>
          <input
            id="eik"
            type="text"
            formControlName="eik"
            pInputText
            class="w-full"
            placeholder="Enter EIK (optional)"
          />
        </div>

        <!-- MOL -->
        <div>
          <label for="mol" class="block text-sm font-medium text-blue-700 mb-2">
            MOL (Материално отговорно лице)
          </label>
          <input
            id="mol"
            type="text"
            formControlName="mol"
            pInputText
            class="w-full"
            placeholder="Enter MOL (optional)"
          />
        </div>
      </div>

      <!-- Submit Button -->
      <div class="pt-6">
        <button
          type="submit"
          [disabled]="!isFormValid || loading"
          class="w-full px-6 py-4 bg-gradient-to-r from-[#d734b1] to-[#7125bb] text-white font-semibold rounded-lg shadow-lg hover:from-[#7125bb] hover:to-[#d734b1] transition-all transform hover:scale-105 disabled:opacity-50 disabled:transform-none flex items-center justify-center"
        >
          <p-progressSpinner
            *ngIf="loading"
            styleClass="w-5 h-5 mr-2"
            strokeWidth="4"
          ></p-progressSpinner>
          {{ loading ? 'Saving...' : 'Save Billing Information' }}
        </button>
      </div>
    </div>
  </form>
</div>
