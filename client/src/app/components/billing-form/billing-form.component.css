/* Custom styles for the billing form component */
:host {
  display: block;
  color-scheme: light !important;
}

/* Force light mode and disable dark theme */
:host,
:host * {
  color-scheme: light !important;
}

/* Disable dark mode media query */
@media (prefers-color-scheme: dark) {
  :host,
  :host * {
    color-scheme: light !important;
    background-color: white !important;
    color: #374151 !important;
  }
}

/* Fade-in animation */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Fix input text color and styling */
::ng-deep .p-inputtext {
  color: #374151 !important;
  background-color: white !important;
  border: 1px solid #d7b4f4 !important;
  border-radius: 0.5rem !important;
  padding: 0.75rem 1rem !important;
  font-size: 1rem !important;
  transition: all 0.2s ease-in-out !important;
}

::ng-deep .p-inputtext:focus {
  border-color: #7125bb !important;
  box-shadow: 0 0 0 2px rgba(113, 37, 187, 0.2) !important;
  outline: none !important;
}

::ng-deep .p-inputtext::placeholder {
  color: #9ca3af !important;
}

/* Select dropdown styling */
::ng-deep .p-select {
  border: 1px solid #d7b4f4 !important;
  border-radius: 0.5rem !important;
  background-color: white !important;
  color: #374151 !important;
}

::ng-deep .p-select:not(.p-disabled):hover {
  border-color: #7125bb !important;
}

::ng-deep .p-select:not(.p-disabled).p-focus {
  border-color: #7125bb !important;
  box-shadow: 0 0 0 2px rgba(113, 37, 187, 0.2) !important;
}

::ng-deep .p-select .p-select-label {
  color: #374151 !important;
  padding: 0.75rem 1rem !important;
  font-size: 1rem !important;
  background-color: transparent !important;
}

::ng-deep .p-select .p-select-label.p-placeholder {
  color: #9ca3af !important;
}

::ng-deep .p-select-overlay {
  border: 1px solid #d7b4f4 !important;
  border-radius: 0.5rem !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
  background-color: white !important;
}

::ng-deep .p-select-list {
  background-color: white !important;
}

::ng-deep .p-select-option {
  color: #374151 !important;
  padding: 0.75rem 1rem !important;
  background-color: white !important;
}

::ng-deep .p-select-option:hover {
  background-color: #f5f0ff !important;
  color: #7125bb !important;
}

::ng-deep .p-select-option.p-selected {
  background-color: #7125bb !important;
  color: white !important;
}

::ng-deep .p-select-option.p-focused {
  background-color: #f5f0ff !important;
  color: #7125bb !important;
}

/* Force override any dark theme */
::ng-deep .p-select,
::ng-deep .p-select *:not(.p-select-option.p-selected):not(.p-select-option.p-selected *) {
  color: #374151 !important;
  background-color: white !important;
}

::ng-deep .p-select .p-select-label.p-placeholder {
  color: #9ca3af !important;
}

::ng-deep .p-select-option.p-selected,
::ng-deep .p-select-option.p-selected * {
  color: white !important;
  background-color: #7125bb !important;
}

/* Additional specific overrides */
::ng-deep .p-select-overlay .p-select-list .p-select-option {
  color: #374151 !important;
  background-color: white !important;
}

::ng-deep .p-select-overlay .p-select-list .p-select-option:not(.p-selected):not(.p-focused) {
  color: #374151 !important;
  background-color: white !important;
}

::ng-deep .p-select-overlay .p-select-list .p-select-option.p-focused:not(.p-selected) {
  color: #7125bb !important;
  background-color: #f5f0ff !important;
}

/* Ensure dropdown arrow is visible */
::ng-deep .p-select .p-select-dropdown {
  color: #374151 !important;
}

/* Progress spinner styling */
::ng-deep .p-progress-spinner-circle {
  stroke: currentColor !important;
}
