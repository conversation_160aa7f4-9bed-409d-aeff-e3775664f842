/* Custom styles for the billing form component */
:host {
  display: block;
}

/* Ensure form inputs have consistent styling */
input[pInputText]:focus {
  box-shadow: 0 0 0 2px rgba(113, 37, 187, 0.2);
}

/* Custom checkbox styling to match the theme */
::ng-deep .p-checkbox .p-checkbox-box {
  border-color: #d7b4f4;
}

::ng-deep .p-checkbox .p-checkbox-box.p-highlight {
  background: linear-gradient(135deg, #d734b1, #7125bb);
  border-color: #7125bb;
}

/* Progress spinner styling */
::ng-deep .p-progress-spinner-circle {
  stroke: currentColor;
}
