import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Proposal } from '../../lib/models';
import { Observable } from 'rxjs';
import {
  ProposalDraft,
  ProposalDraftRequest,
  ProposalRequest,
} from '../../lib/validators';

@Injectable({
  providedIn: 'root',
})
export class ProposalService {
  constructor(private http: HttpClient) {}

  getAll(): Observable<Proposal[]> {
    return this.http.get<Proposal[]>('/api/proposals');
  }

  get(id: string): Observable<Proposal> {
    return this.http.get<Proposal>(`/api/proposals/${id}`);
  }

  pay(id: string): Observable<{ url: string }> {
    return this.http.post<{ url: string }>(`/api/proposals/${id}/payments`, {});
  }

  create(model: ProposalRequest): Observable<Proposal> {
    return this.http.post<Proposal>(`/api/proposals`, model);
  }

  createDraft(model: ProposalDraftRequest): Observable<ProposalDraft> {
    return this.http.post<ProposalDraft>(`/api/proposals/draft`, model);
  }

  update(id: string, model: ProposalRequest): Observable<void> {
    return this.http.put<void>(`/api/proposals/${id}`, model);
  }

  generateInvoice(proposalId: string): Observable<{
    success: boolean;
    message: string;
    invoice_id?: string;
    invoice_type?: 'initial' | 'final';
  }> {
    return this.http.post<{
      success: boolean;
      message: string;
      invoice_id?: string;
      invoice_type?: 'initial' | 'final';
    }>('/api/proposals/invoices', { proposal_id: proposalId });
  }

  downloadInvoice(proposalId: string, type: 'initial' | 'final'): Observable<Blob> {
    return this.http.get(`/api/proposals/${proposalId}/invoice/${type}/download`, {
      responseType: 'blob'
    });
  }
}
