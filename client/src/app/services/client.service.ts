import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ClientRequest, BillingInformationRequest, BillingInformation } from '../../lib/validators';
import { Observable } from 'rxjs';
import { Client } from '../../lib/models';

@Injectable({
  providedIn: 'root',
})
export class ClientService {
  constructor(private http: HttpClient) {}

  getAll(): Observable<Client[]> {
    return this.http.get<Client[]>('/api/clients');
  }

  create(model: ClientRequest): Observable<Client> {
    return this.http.post<Client>('/api/clients', model);
  }

  update(id: string, model: ClientRequest): Observable<void> {
    return this.http.put<void>(`/api/clients/${id}`, model);
  }

  delete(id: string): Observable<void> {
    return this.http.delete<void>(`/api/clients/${id}`);
  }

  getBillingInformation(clientId: string): Observable<BillingInformation | null> {
    return this.http.get<BillingInformation | null>(`/api/clients/${clientId}/billing`);
  }

  createBillingInformation(clientId: string, model: BillingInformationRequest): Observable<BillingInformation> {
    return this.http.post<BillingInformation>(`/api/clients/${clientId}/billing`, model);
  }

  updateBillingInformation(clientId: string, model: BillingInformationRequest): Observable<BillingInformation> {
    return this.http.put<BillingInformation>(`/api/clients/${clientId}/billing`, model);
  }
}
